package com.maguo.loan.cash.flow.service.common.loan;

import com.maguo.loan.cash.flow.common.BizException;
import com.maguo.loan.cash.flow.common.ResultCode;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.common.ProjectElements;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.ProjectElementsRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class LoanCommonCheckService {
    private static final Logger logger = LoggerFactory.getLogger(LoanCommonService.class);
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");

    @Autowired
    private ProjectElementsRepository projectElementsRepository;

    @Autowired
    private LoanRepository loanRepository;

    /**
     * 统一校验放款阶段参数
     *
     * @param order 放款信息
     */
    public void checkLoanParameters(Order order)  {
        logger.info("开始校验放款参数, loanId: {}", order.getId());

        // 获取项目要素
        ProjectElements projectElements = projectElementsRepository.findByProjectCode(order.getProjectCode());
        if (Objects.isNull(projectElements)) {
            logger.error("未找到项目要素, projectCode: {}", order.getProjectCode());
            throw new BizException(ResultCode.PROJECT_ELEMENTS_NOT_FOUND);
        }
        //校验还款类型
        //todo 目前绿信放款接口未传该字段，产品会与绿信沟通添加
        checkRepaymentType(order, projectElements.getSupportedRepayTypes());

        // 校验借款期限
        checkLoanTerms(order.getApplyPeriods(), projectElements.getLoanTerms());

        // 校验单笔提现步长
        checkDrawableAmountStep(order.getApplyAmount(), projectElements.getDrawableAmountStep());

        logger.info("放款参数校验通过, loanId: {}", order.getId());
    }

    /**
     * 校验还款类型
     */
    private void checkRepaymentType(Order order, String supportedRepayTypes) {
        // 1. 检查还款类型是否为空
        if (Objects.isNull(supportedRepayTypes)) {
            throw new BizException(ResultCode.REPAYMENT_TYPE_REQUIRED);
        }

        // 2. 校验还款类型 获取项目支持的还款类型supportedRepayTypes
        // TODO: 2025/8/19  loan.getLoanNo()待确定后替换为还款类型 目前绿信放款接口未传该字段，产品会与绿信沟通添加
        if (!order.getId().equals(supportedRepayTypes)) {
            logger.error("还款类型不支持, 支持的还款类型: {}, 传入的还款类型: {}", supportedRepayTypes, order.getId());
            throw new BizException(ResultCode.UNSUPPORTED_REPAYMENT_TYPE);
        }
    }

    /**
     * 校验借款期限
     *
     * @param applyTerms   申请的借款期限
     * @param allowedTerms 允许的借款期限 (英文逗号分隔)
     */
    private void checkLoanTerms(Integer applyTerms, String allowedTerms)  {
        if (allowedTerms == null || allowedTerms.isEmpty()) {
            logger.info("未设置允许的借款期限，跳过校验");
            return;
        }

        Set<Integer> allowedTermsSet = new HashSet<>();
        String[] termsArray = allowedTerms.split(",");
        for (String term : termsArray) {
            try {
                allowedTermsSet.add(Integer.parseInt(term.trim()));
            } catch (NumberFormatException e) {
                logger.error("借款期限格式错误: {}", term, e);
                throw new BizException(ResultCode.LOAN_TERMS_FORMAT_ERROR);
            }
        }

        if (!allowedTermsSet.contains(applyTerms)) {
            logger.warn("借款期限不在允许范围内: {}, 允许的期限: {}", applyTerms, allowedTerms);
            throw new BizException(ResultCode.LOAN_TERM_NOT_IN_ALLOWED_RANGE);
        }
    }

    /**
     * 校验单笔提现步长
     *
     * @param amount 提现金额
     * @param step   提现步长
     */
    private void checkDrawableAmountStep(BigDecimal amount, String step) {
        if (step == null || step.isEmpty()) {
            logger.info("未设置单笔提现步长，跳过校验");
            return;
        }

        try {
            BigDecimal stepValue = new BigDecimal(step);
            if (stepValue.compareTo(BigDecimal.ZERO) <= 0) {
                logger.error("单笔提现步长必须大于0: {}", step);
                throw new BizException(ResultCode.DRAWABLE_AMOUNT_STEP_INVALID);
            }

            // 检查金额是否是步长的整数倍
            BigDecimal remainder = amount.remainder(stepValue);
            if (remainder.compareTo(BigDecimal.ZERO) != 0) {
                logger.warn("提现金额不是步长的整数倍: {}, 步长: {}", amount, step);
                throw new BizException(ResultCode.DRAWABLE_AMOUNT_STEP_NOT_MATCH);
            }
        } catch (NumberFormatException e) {
            logger.error("单笔提现步长格式错误: {}", step, e);
            throw new BizException(ResultCode.DRAWABLE_AMOUNT_STEP_FORMAT_ERROR);
        }
    }
}
