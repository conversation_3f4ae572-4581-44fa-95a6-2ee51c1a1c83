package com.jinghang.cash.modules.manage.mapstruct;

import com.jinghang.cash.enums.AbleStatus;
import com.jinghang.cash.enums.FlowCapitalEnable;
import com.jinghang.cash.enums.ProtocolChannel;
import com.jinghang.cash.enums.WhetherState;
import com.jinghang.cash.modules.manage.vo.PreOrderVo;
import com.jinghang.cash.modules.manage.vo.req.CapitalConfigRequest;
import com.jinghang.cash.modules.manage.vo.req.FlowRouteConfigRequest;
import com.jinghang.cash.modules.manage.vo.req.PreOrderReq;
import com.jinghang.cash.modules.manage.vo.res.BankChannelResponse;
import com.jinghang.cash.modules.manage.vo.res.CapitalConfigInfoResponse;
import com.jinghang.cash.modules.manage.vo.res.CapitalConfigResponse;
import com.jinghang.cash.modules.manage.vo.res.FlowProtocolChannelResponse;
import com.jinghang.cash.modules.manage.vo.res.FlowRouteConfigResponse;
import com.jinghang.cash.modules.manage.vo.res.OfflineRepayApplyResponse;
import com.jinghang.cash.modules.manage.vo.res.PaymentChannelConfigResponse;
import com.jinghang.cash.modules.manage.vo.res.RevolvingStrategyConfigResponse;
import com.jinghang.cash.modules.manage.vo.res.RevolvingStrategyRelateResponse;
import com.jinghang.cash.modules.manage.vo.res.RightsBasePackageResponse;
import com.jinghang.cash.modules.manage.vo.rsp.PreOrderResp;
import com.jinghang.cash.pojo.CapitalConfig;
import com.jinghang.cash.pojo.FlowConfig;
import com.jinghang.cash.pojo.OfflineRepayApply;
import com.jinghang.cash.pojo.PaymentChannelConfig;
import com.jinghang.cash.pojo.RevolvingStrategyConfig;
import com.jinghang.cash.pojo.RevolvingStrategyRelate;
import com.jinghang.cash.pojo.RightsBasePackage;
import com.jinghang.cash.pojo.RightsConfig;
import com.jinghang.ppd.api.dto.route.CapitalConfigDTO;
import com.jinghang.ppd.api.dto.route.FlowRouteConfigDTO;
import com.jinghang.ppd.api.enums.ValidStatus;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T09:59:15+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
public class ManageMapstructImpl implements ManageMapstruct {

    @Override
    public OfflineRepayApplyResponse copyOfflineRepayApplyToOfflineRepayApplyResponse(OfflineRepayApply offlineRepayApply) {
        if ( offlineRepayApply == null ) {
            return null;
        }

        OfflineRepayApplyResponse offlineRepayApplyResponse = new OfflineRepayApplyResponse();

        offlineRepayApplyResponse.setId( offlineRepayApply.getId() );
        offlineRepayApplyResponse.setOrderId( offlineRepayApply.getOrderId() );
        offlineRepayApplyResponse.setApplyState( offlineRepayApply.getApplyState() );
        offlineRepayApplyResponse.setRepayPurpose( offlineRepayApply.getRepayPurpose() );
        offlineRepayApplyResponse.setAmount( offlineRepayApply.getAmount() );
        offlineRepayApplyResponse.setActAmount( offlineRepayApply.getActAmount() );
        offlineRepayApplyResponse.setReduceAmount( offlineRepayApply.getReduceAmount() );
        offlineRepayApplyResponse.setCreatedBy( offlineRepayApply.getCreatedBy() );
        offlineRepayApplyResponse.setCreatedTime( offlineRepayApply.getCreatedTime() );
        offlineRepayApplyResponse.setUpdatedBy( offlineRepayApply.getUpdatedBy() );
        offlineRepayApplyResponse.setUpdatedTime( offlineRepayApply.getUpdatedTime() );
        offlineRepayApplyResponse.setOverflowAmount( offlineRepayApply.getOverflowAmount() );

        ManageMapstruct.afterCopyOfflineRepayApplyToOfflineRepayApplyResponse( offlineRepayApply, offlineRepayApplyResponse );

        return offlineRepayApplyResponse;
    }

    @Override
    public List<CapitalConfigResponse> copyCapitalConfigsToCapitalConfigResponses(List<CapitalConfig> list) {
        if ( list == null ) {
            return null;
        }

        List<CapitalConfigResponse> list1 = new ArrayList<CapitalConfigResponse>( list.size() );
        for ( CapitalConfig capitalConfig : list ) {
            list1.add( capitalConfigToCapitalConfigResponse( capitalConfig ) );
        }

        return list1;
    }

    @Override
    public CapitalConfig copyCapitalConfigRequestToCapitalConfig(CapitalConfigRequest reqVO) {
        if ( reqVO == null ) {
            return null;
        }

        CapitalConfig capitalConfig = new CapitalConfig();

        if ( reqVO.getCreditTimeStatus() != null ) {
            capitalConfig.setCreditTimeStatus( reqVO.getCreditTimeStatus().name() );
        }
        if ( reqVO.getLoanTimeStatus() != null ) {
            capitalConfig.setLoanTimeStatus( reqVO.getLoanTimeStatus().name() );
        }
        if ( reqVO.getRepayTimeStatus() != null ) {
            capitalConfig.setRepayTimeStatus( reqVO.getRepayTimeStatus().name() );
        }
        capitalConfig.setId( reqVO.getId() );
        capitalConfig.setBankChannel( reqVO.getBankChannel() );
        if ( reqVO.getGuaranteeCompany() != null ) {
            capitalConfig.setGuaranteeCompany( reqVO.getGuaranteeCompany().name() );
        }
        capitalConfig.setCreditDayLimit( reqVO.getCreditDayLimit() );
        capitalConfig.setLoanDayLimit( reqVO.getLoanDayLimit() );
        capitalConfig.setPeriodsRange( reqVO.getPeriodsRange() );
        capitalConfig.setAgesRange( reqVO.getAgesRange() );
        capitalConfig.setSingleAmtRange( reqVO.getSingleAmtRange() );
        capitalConfig.setCreditStartTime( reqVO.getCreditStartTime() );
        capitalConfig.setCreditEndTime( reqVO.getCreditEndTime() );
        capitalConfig.setLoanStartTime( reqVO.getLoanStartTime() );
        capitalConfig.setLoanEndTime( reqVO.getLoanEndTime() );
        capitalConfig.setEnabled( reqVO.getEnabled() );
        capitalConfig.setBankRate( reqVO.getBankRate() );
        capitalConfig.setSupportIrrLevel( reqVO.getSupportIrrLevel() );
        capitalConfig.setRepayStartTime( reqVO.getRepayStartTime() );
        capitalConfig.setRepayEndTime( reqVO.getRepayEndTime() );
        capitalConfig.setRenewedFlag( reqVO.getRenewedFlag() );
        if ( reqVO.getProtocolChannel() != null ) {
            capitalConfig.setProtocolChannel( reqVO.getProtocolChannel().name() );
        }

        return capitalConfig;
    }

    @Override
    public CapitalConfigInfoResponse copyCapitalConfigToCapitalConfigInfoResponse(CapitalConfig entity) {
        if ( entity == null ) {
            return null;
        }

        CapitalConfigInfoResponse capitalConfigInfoResponse = new CapitalConfigInfoResponse();

        capitalConfigInfoResponse.setId( entity.getId() );
        capitalConfigInfoResponse.setEnabled( entity.getEnabled() );
        capitalConfigInfoResponse.setBankChannel( entity.getBankChannel() );
        capitalConfigInfoResponse.setGuaranteeCompany( entity.getGuaranteeCompany() );
        capitalConfigInfoResponse.setBankRate( entity.getBankRate() );
        capitalConfigInfoResponse.setSupportIrrLevel( entity.getSupportIrrLevel() );
        capitalConfigInfoResponse.setCreditDayLimit( entity.getCreditDayLimit() );
        capitalConfigInfoResponse.setLoanDayLimit( entity.getLoanDayLimit() );
        capitalConfigInfoResponse.setAgesRange( entity.getAgesRange() );
        capitalConfigInfoResponse.setSingleAmtRange( entity.getSingleAmtRange() );
        if ( entity.getCreditTimeStatus() != null ) {
            capitalConfigInfoResponse.setCreditTimeStatus( Enum.valueOf( AbleStatus.class, entity.getCreditTimeStatus() ) );
        }
        if ( entity.getLoanTimeStatus() != null ) {
            capitalConfigInfoResponse.setLoanTimeStatus( Enum.valueOf( AbleStatus.class, entity.getLoanTimeStatus() ) );
        }
        if ( entity.getRepayTimeStatus() != null ) {
            capitalConfigInfoResponse.setRepayTimeStatus( Enum.valueOf( AbleStatus.class, entity.getRepayTimeStatus() ) );
        }
        capitalConfigInfoResponse.setCreditStartTime( entity.getCreditStartTime() );
        capitalConfigInfoResponse.setCreditEndTime( entity.getCreditEndTime() );
        capitalConfigInfoResponse.setLoanStartTime( entity.getLoanStartTime() );
        capitalConfigInfoResponse.setLoanEndTime( entity.getLoanEndTime() );
        capitalConfigInfoResponse.setRepayStartTime( entity.getRepayStartTime() );
        capitalConfigInfoResponse.setRepayEndTime( entity.getRepayEndTime() );
        capitalConfigInfoResponse.setPeriodsRange( entity.getPeriodsRange() );
        if ( entity.getRenewedFlag() != null ) {
            capitalConfigInfoResponse.setRenewedFlag( entity.getRenewedFlag().name() );
        }
        if ( entity.getProtocolChannel() != null ) {
            capitalConfigInfoResponse.setProtocolChannel( Enum.valueOf( ProtocolChannel.class, entity.getProtocolChannel() ) );
        }

        return capitalConfigInfoResponse;
    }

    @Override
    public List<FlowRouteConfigResponse> copyCapitalConfigToFlowRouteConfigResponse(List<CapitalConfig> list) {
        if ( list == null ) {
            return null;
        }

        List<FlowRouteConfigResponse> list1 = new ArrayList<FlowRouteConfigResponse>( list.size() );
        for ( CapitalConfig capitalConfig : list ) {
            list1.add( copyCapitalConfigToFlowRouteConfigResponse( capitalConfig ) );
        }

        return list1;
    }

    @Override
    public FlowRouteConfigResponse copyCapitalConfigToFlowRouteConfigResponse(CapitalConfig capitalConfig) {
        if ( capitalConfig == null ) {
            return null;
        }

        FlowRouteConfigResponse flowRouteConfigResponse = new FlowRouteConfigResponse();

        flowRouteConfigResponse.setCapitalId( capitalConfig.getId() );
        flowRouteConfigResponse.setBankChannel( capitalConfig.getBankChannel() );
        flowRouteConfigResponse.setEnabled( capitalConfig.getEnabled() );

        return flowRouteConfigResponse;
    }

    @Override
    public CapitalConfigDTO copyEntityToCapitalConfigDTO(CapitalConfig capitalConfig) {
        if ( capitalConfig == null ) {
            return null;
        }

        CapitalConfigDTO capitalConfigDTO = new CapitalConfigDTO();

        capitalConfigDTO.setBankRate( capitalConfig.getBankRate() );
        capitalConfigDTO.setId( capitalConfig.getId() );
        capitalConfigDTO.setEnabled( capitalConfig.getEnabled() );
        capitalConfigDTO.setBankChannel( capitalConfig.getBankChannel() );
        capitalConfigDTO.setSupportIrrLevel( capitalConfig.getSupportIrrLevel() );
        capitalConfigDTO.setCreditDayLimit( capitalConfig.getCreditDayLimit() );
        capitalConfigDTO.setLoanDayLimit( capitalConfig.getLoanDayLimit() );
        capitalConfigDTO.setAgesRange( capitalConfig.getAgesRange() );
        capitalConfigDTO.setSingleAmtRange( capitalConfig.getSingleAmtRange() );
        capitalConfigDTO.setCreditStartTime( capitalConfig.getCreditStartTime() );
        capitalConfigDTO.setCreditEndTime( capitalConfig.getCreditEndTime() );
        capitalConfigDTO.setLoanStartTime( capitalConfig.getLoanStartTime() );
        capitalConfigDTO.setLoanEndTime( capitalConfig.getLoanEndTime() );
        capitalConfigDTO.setRemark( capitalConfig.getRemark() );
        if ( capitalConfig.getRevision() != null ) {
            capitalConfigDTO.setRevision( Integer.parseInt( capitalConfig.getRevision() ) );
        }
        capitalConfigDTO.setCreatedBy( capitalConfig.getCreatedBy() );
        capitalConfigDTO.setCreatedTime( capitalConfig.getCreatedTime() );
        capitalConfigDTO.setUpdatedBy( capitalConfig.getUpdatedBy() );
        capitalConfigDTO.setUpdatedTime( capitalConfig.getUpdatedTime() );
        capitalConfigDTO.setRepayStartTime( capitalConfig.getRepayStartTime() );
        capitalConfigDTO.setRepayEndTime( capitalConfig.getRepayEndTime() );
        capitalConfigDTO.setPeriodsRange( capitalConfig.getPeriodsRange() );
        if ( capitalConfig.getCreditTimeStatus() != null ) {
            capitalConfigDTO.setCreditTimeStatus( Enum.valueOf( com.jinghang.ppd.api.enums.AbleStatus.class, capitalConfig.getCreditTimeStatus() ) );
        }
        if ( capitalConfig.getLoanTimeStatus() != null ) {
            capitalConfigDTO.setLoanTimeStatus( Enum.valueOf( com.jinghang.ppd.api.enums.AbleStatus.class, capitalConfig.getLoanTimeStatus() ) );
        }
        if ( capitalConfig.getRepayTimeStatus() != null ) {
            capitalConfigDTO.setRepayTimeStatus( Enum.valueOf( com.jinghang.ppd.api.enums.AbleStatus.class, capitalConfig.getRepayTimeStatus() ) );
        }
        capitalConfigDTO.setGuaranteeCompany( capitalConfig.getGuaranteeCompany() );
        if ( capitalConfig.getRenewedFlag() != null ) {
            capitalConfigDTO.setRenewedFlag( capitalConfig.getRenewedFlag().name() );
        }
        if ( capitalConfig.getProtocolChannel() != null ) {
            capitalConfigDTO.setProtocolChannel( Enum.valueOf( com.jinghang.ppd.api.enums.ProtocolChannel.class, capitalConfig.getProtocolChannel() ) );
        }

        return capitalConfigDTO;
    }

    @Override
    public List<BankChannelResponse> copyFlowConfigToBankChannelResponse(List<FlowConfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<BankChannelResponse> list = new ArrayList<BankChannelResponse>( entityList.size() );
        for ( FlowConfig flowConfig : entityList ) {
            list.add( copyFlowConfigToBankChannelResponse( flowConfig ) );
        }

        return list;
    }

    @Override
    public BankChannelResponse copyFlowConfigToBankChannelResponse(FlowConfig entity) {
        if ( entity == null ) {
            return null;
        }

        BankChannelResponse bankChannelResponse = new BankChannelResponse();

        bankChannelResponse.setName( entity.getFlowChannel() );
        bankChannelResponse.setId( entity.getId() );

        return bankChannelResponse;
    }

    @Override
    public FlowRouteConfigDTO copyFlowRouteConfigRequestToFlowRouteConfigDTO(FlowRouteConfigRequest reqVO) {
        if ( reqVO == null ) {
            return null;
        }

        FlowRouteConfigDTO flowRouteConfigDTO = new FlowRouteConfigDTO();

        flowRouteConfigDTO.setFlowId( reqVO.getFlowId() );
        flowRouteConfigDTO.setList( flowRouteConfigReqListToFlowRouteConfigReqList( reqVO.getList() ) );

        return flowRouteConfigDTO;
    }

    @Override
    public List<BankChannelResponse> copyCapitalConfigToBankChannelResponse(List<CapitalConfig> list) {
        if ( list == null ) {
            return null;
        }

        List<BankChannelResponse> list1 = new ArrayList<BankChannelResponse>( list.size() );
        for ( CapitalConfig capitalConfig : list ) {
            list1.add( copyCapitalConfigToBankChannelResponse( capitalConfig ) );
        }

        return list1;
    }

    @Override
    public BankChannelResponse copyCapitalConfigToBankChannelResponse(CapitalConfig capitalConfig) {
        if ( capitalConfig == null ) {
            return null;
        }

        BankChannelResponse bankChannelResponse = new BankChannelResponse();

        bankChannelResponse.setName( capitalConfig.getBankChannel() );
        bankChannelResponse.setId( capitalConfig.getId() );

        return bankChannelResponse;
    }

    @Override
    public List<RightsBasePackageResponse> copyRightsBasePackageToVO(List<RightsBasePackage> list) {
        if ( list == null ) {
            return null;
        }

        List<RightsBasePackageResponse> list1 = new ArrayList<RightsBasePackageResponse>( list.size() );
        for ( RightsBasePackage rightsBasePackage : list ) {
            list1.add( copyRightsBasePackageToVO( rightsBasePackage ) );
        }

        return list1;
    }

    @Override
    public RightsBasePackageResponse copyRightsBasePackageToVO(RightsBasePackage rightsConfig) {
        if ( rightsConfig == null ) {
            return null;
        }

        RightsBasePackageResponse rightsBasePackageResponse = new RightsBasePackageResponse();

        rightsBasePackageResponse.setId( rightsConfig.getId() );
        rightsBasePackageResponse.setCode( rightsConfig.getCode() );
        rightsBasePackageResponse.setPackageName( rightsConfig.getPackageName() );
        rightsBasePackageResponse.setCostingPrice( rightsConfig.getCostingPrice() );
        rightsBasePackageResponse.setSellingPrice( rightsConfig.getSellingPrice() );
        rightsBasePackageResponse.setUseStatus( rightsConfig.getUseStatus() );
        rightsBasePackageResponse.setUpdatedBy( rightsConfig.getUpdatedBy() );
        if ( rightsConfig.getUpdatedTime() != null ) {
            rightsBasePackageResponse.setUpdatedTime( Date.from( rightsConfig.getUpdatedTime().toInstant( ZoneOffset.UTC ) ) );
        }

        return rightsBasePackageResponse;
    }

    @Override
    public List<RightsBasePackageResponse> copyRightsConfigToVOS(List<RightsConfig> list) {
        if ( list == null ) {
            return null;
        }

        List<RightsBasePackageResponse> list1 = new ArrayList<RightsBasePackageResponse>( list.size() );
        for ( RightsConfig rightsConfig : list ) {
            list1.add( copyRightsConfigToVO( rightsConfig ) );
        }

        return list1;
    }

    @Override
    public RightsBasePackageResponse copyRightsConfigToVO(RightsConfig entity) {
        if ( entity == null ) {
            return null;
        }

        RightsBasePackageResponse rightsBasePackageResponse = new RightsBasePackageResponse();

        rightsBasePackageResponse.setId( entity.getId() );
        rightsBasePackageResponse.setRiskLevel( entity.getRiskLevel() );
        rightsBasePackageResponse.setSellingPrice( entity.getSellingPrice() );
        rightsBasePackageResponse.setApproveRightsForce( entity.getApproveRightsForce() );
        rightsBasePackageResponse.setUseStatus( entity.getUseStatus() );
        rightsBasePackageResponse.setFlowChannel( entity.getFlowChannel() );
        rightsBasePackageResponse.setUpdatedBy( entity.getUpdatedBy() );
        if ( entity.getUpdatedTime() != null ) {
            rightsBasePackageResponse.setUpdatedTime( Date.from( entity.getUpdatedTime().toInstant( ZoneOffset.UTC ) ) );
        }

        return rightsBasePackageResponse;
    }

    @Override
    public PreOrderVo toPreOrderVo(PreOrderReq req) {
        if ( req == null ) {
            return null;
        }

        PreOrderVo preOrderVo = new PreOrderVo();

        preOrderVo.setFlowChannel( req.getFlowChannel() );
        preOrderVo.setMobile( req.getMobile() );
        preOrderVo.setCertNo( req.getCertNo() );
        preOrderVo.setApplyTimeStart( req.getApplyTimeStart() );
        preOrderVo.setApplyTimeEnd( req.getApplyTimeEnd() );

        return preOrderVo;
    }

    @Override
    public PreOrderResp toPreOrderResp(PreOrderVo vo) {
        if ( vo == null ) {
            return null;
        }

        PreOrderResp preOrderResp = new PreOrderResp();

        preOrderResp.setId( vo.getId() );
        preOrderResp.setOrderNo( vo.getOrderNo() );
        preOrderResp.setFlowChannel( vo.getFlowChannel() );
        preOrderResp.setOpenId( vo.getOpenId() );
        preOrderResp.setName( vo.getName() );
        preOrderResp.setMobile( vo.getMobile() );
        preOrderResp.setCertNo( vo.getCertNo() );
        preOrderResp.setApplyTime( vo.getApplyTime() );
        preOrderResp.setApplyAmount( vo.getApplyAmount() );
        preOrderResp.setApplyPeriods( vo.getApplyPeriods() );
        preOrderResp.setPreOrderState( vo.getPreOrderState() );
        preOrderResp.setIsReject( vo.getIsReject() );
        preOrderResp.setRiskId( vo.getRiskId() );
        preOrderResp.setRemark( vo.getRemark() );
        preOrderResp.setRevision( vo.getRevision() );
        preOrderResp.setCreatedBy( vo.getCreatedBy() );
        preOrderResp.setCreatedTime( vo.getCreatedTime() );
        preOrderResp.setUpdatedBy( vo.getUpdatedBy() );
        preOrderResp.setUpdatedTime( vo.getUpdatedTime() );
        preOrderResp.setFailReason( vo.getFailReason() );

        return preOrderResp;
    }

    @Override
    public PaymentChannelConfigResponse toPaymentChannelConfigResponse(PaymentChannelConfig config) {
        if ( config == null ) {
            return null;
        }

        PaymentChannelConfigResponse paymentChannelConfigResponse = new PaymentChannelConfigResponse();

        paymentChannelConfigResponse.setId( config.getId() );
        paymentChannelConfigResponse.setPaymentChannelCode( config.getPaymentChannelCode() );
        paymentChannelConfigResponse.setPaymentChannelName( config.getPaymentChannelName() );
        paymentChannelConfigResponse.setSingleAmountUpper( config.getSingleAmountUpper() );
        paymentChannelConfigResponse.setEnabled( config.getEnabled() );
        paymentChannelConfigResponse.setUpdatedBy( config.getUpdatedBy() );
        paymentChannelConfigResponse.setUpdatedTime( config.getUpdatedTime() );

        return paymentChannelConfigResponse;
    }

    @Override
    public List<PaymentChannelConfigResponse> toPaymentChannelConfigResponseList(List<PaymentChannelConfig> list) {
        if ( list == null ) {
            return null;
        }

        List<PaymentChannelConfigResponse> list1 = new ArrayList<PaymentChannelConfigResponse>( list.size() );
        for ( PaymentChannelConfig paymentChannelConfig : list ) {
            list1.add( toPaymentChannelConfigResponse( paymentChannelConfig ) );
        }

        return list1;
    }

    @Override
    public List<FlowProtocolChannelResponse> toFlowProtocolChannelList(List<FlowConfig> configList) {
        if ( configList == null ) {
            return null;
        }

        List<FlowProtocolChannelResponse> list = new ArrayList<FlowProtocolChannelResponse>( configList.size() );
        for ( FlowConfig flowConfig : configList ) {
            list.add( toFlowProtocolChannelDetail( flowConfig ) );
        }

        return list;
    }

    @Override
    public FlowProtocolChannelResponse toFlowProtocolChannelDetail(FlowConfig flowConfig) {
        if ( flowConfig == null ) {
            return null;
        }

        FlowProtocolChannelResponse flowProtocolChannelResponse = new FlowProtocolChannelResponse();

        flowProtocolChannelResponse.setFlowChannel( flowConfig.getFlowChannel() );
        flowProtocolChannelResponse.setFirstProtocolChannel( flowConfig.getFirstProtocolChannel() );
        flowProtocolChannelResponse.setSecondProtocolChannel( flowConfig.getSecondProtocolChannel() );
        flowProtocolChannelResponse.setUpdatedBy( flowConfig.getUpdatedBy() );
        if ( flowConfig.getUpdatedTime() != null ) {
            flowProtocolChannelResponse.setUpdatedTime( Date.from( flowConfig.getUpdatedTime().toInstant( ZoneOffset.UTC ) ) );
        }

        return flowProtocolChannelResponse;
    }

    @Override
    public RevolvingStrategyConfigResponse toRevolvingStrategyConfigResponse(RevolvingStrategyConfig config) {
        if ( config == null ) {
            return null;
        }

        RevolvingStrategyConfigResponse revolvingStrategyConfigResponse = new RevolvingStrategyConfigResponse();

        revolvingStrategyConfigResponse.setOperator( config.getUpdatedBy() );
        revolvingStrategyConfigResponse.setModifyTime( config.getUpdatedTime() );
        revolvingStrategyConfigResponse.setId( config.getId() );
        revolvingStrategyConfigResponse.setStrategyName( config.getStrategyName() );
        revolvingStrategyConfigResponse.setStrategyCode( config.getStrategyCode() );
        revolvingStrategyConfigResponse.setAmountUpper( config.getAmountUpper() );
        revolvingStrategyConfigResponse.setAmountLower( config.getAmountLower() );
        revolvingStrategyConfigResponse.setStrategyState( config.getStrategyState() );

        return revolvingStrategyConfigResponse;
    }

    @Override
    public List<RevolvingStrategyConfigResponse> toRevolvingStrategyConfigResponseList(List<RevolvingStrategyConfig> list) {
        if ( list == null ) {
            return null;
        }

        List<RevolvingStrategyConfigResponse> list1 = new ArrayList<RevolvingStrategyConfigResponse>( list.size() );
        for ( RevolvingStrategyConfig revolvingStrategyConfig : list ) {
            list1.add( toRevolvingStrategyConfigResponse( revolvingStrategyConfig ) );
        }

        return list1;
    }

    @Override
    public RevolvingStrategyRelateResponse toRevolvingStrategyRelateResponse(RevolvingStrategyRelate revolvingStrategyRelate) {
        if ( revolvingStrategyRelate == null ) {
            return null;
        }

        RevolvingStrategyRelateResponse revolvingStrategyRelateResponse = new RevolvingStrategyRelateResponse();

        revolvingStrategyRelateResponse.setId( revolvingStrategyRelate.getId() );
        revolvingStrategyRelateResponse.setStrategyId( revolvingStrategyRelate.getStrategyId() );
        revolvingStrategyRelateResponse.setRelateCode( revolvingStrategyRelate.getRelateCode() );
        revolvingStrategyRelateResponse.setFrontAmount( revolvingStrategyRelate.getFrontAmount() );
        revolvingStrategyRelateResponse.setRatio( revolvingStrategyRelate.getRatio() );

        return revolvingStrategyRelateResponse;
    }

    @Override
    public List<RevolvingStrategyRelateResponse> toRevolvingStrategyRelateResponseList(List<RevolvingStrategyRelate> revolvingStrategyRelateList) {
        if ( revolvingStrategyRelateList == null ) {
            return null;
        }

        List<RevolvingStrategyRelateResponse> list = new ArrayList<RevolvingStrategyRelateResponse>( revolvingStrategyRelateList.size() );
        for ( RevolvingStrategyRelate revolvingStrategyRelate : revolvingStrategyRelateList ) {
            list.add( toRevolvingStrategyRelateResponse( revolvingStrategyRelate ) );
        }

        return list;
    }

    @Override
    public CapitalConfig copyCapitalConfigDTOToCapitalConfig(CapitalConfigDTO reqDTO) {
        if ( reqDTO == null ) {
            return null;
        }

        CapitalConfig capitalConfig = new CapitalConfig();

        if ( reqDTO.getCreditTimeStatus() != null ) {
            capitalConfig.setCreditTimeStatus( reqDTO.getCreditTimeStatus().name() );
        }
        if ( reqDTO.getLoanTimeStatus() != null ) {
            capitalConfig.setLoanTimeStatus( reqDTO.getLoanTimeStatus().name() );
        }
        if ( reqDTO.getRepayTimeStatus() != null ) {
            capitalConfig.setRepayTimeStatus( reqDTO.getRepayTimeStatus().name() );
        }
        capitalConfig.setId( reqDTO.getId() );
        capitalConfig.setBankChannel( reqDTO.getBankChannel() );
        capitalConfig.setGuaranteeCompany( reqDTO.getGuaranteeCompany() );
        capitalConfig.setCreditDayLimit( reqDTO.getCreditDayLimit() );
        capitalConfig.setLoanDayLimit( reqDTO.getLoanDayLimit() );
        capitalConfig.setPeriodsRange( reqDTO.getPeriodsRange() );
        capitalConfig.setAgesRange( reqDTO.getAgesRange() );
        capitalConfig.setSingleAmtRange( reqDTO.getSingleAmtRange() );
        capitalConfig.setCreditStartTime( reqDTO.getCreditStartTime() );
        capitalConfig.setCreditEndTime( reqDTO.getCreditEndTime() );
        capitalConfig.setLoanStartTime( reqDTO.getLoanStartTime() );
        capitalConfig.setLoanEndTime( reqDTO.getLoanEndTime() );
        capitalConfig.setEnabled( reqDTO.getEnabled() );
        capitalConfig.setBankRate( reqDTO.getBankRate() );
        capitalConfig.setSupportIrrLevel( reqDTO.getSupportIrrLevel() );
        capitalConfig.setRepayStartTime( reqDTO.getRepayStartTime() );
        capitalConfig.setRepayEndTime( reqDTO.getRepayEndTime() );
        if ( reqDTO.getRenewedFlag() != null ) {
            capitalConfig.setRenewedFlag( Enum.valueOf( WhetherState.class, reqDTO.getRenewedFlag() ) );
        }
        if ( reqDTO.getProtocolChannel() != null ) {
            capitalConfig.setProtocolChannel( reqDTO.getProtocolChannel().name() );
        }
        capitalConfig.setRemark( reqDTO.getRemark() );
        if ( reqDTO.getRevision() != null ) {
            capitalConfig.setRevision( String.valueOf( reqDTO.getRevision() ) );
        }
        capitalConfig.setCreatedBy( reqDTO.getCreatedBy() );
        capitalConfig.setCreatedTime( reqDTO.getCreatedTime() );
        capitalConfig.setUpdatedBy( reqDTO.getUpdatedBy() );
        capitalConfig.setUpdatedTime( reqDTO.getUpdatedTime() );

        return capitalConfig;
    }

    protected CapitalConfigResponse capitalConfigToCapitalConfigResponse(CapitalConfig capitalConfig) {
        if ( capitalConfig == null ) {
            return null;
        }

        CapitalConfigResponse capitalConfigResponse = new CapitalConfigResponse();

        capitalConfigResponse.setId( capitalConfig.getId() );
        capitalConfigResponse.setBankChannel( capitalConfig.getBankChannel() );
        capitalConfigResponse.setCreditDayLimit( capitalConfig.getCreditDayLimit() );
        capitalConfigResponse.setLoanDayLimit( capitalConfig.getLoanDayLimit() );
        capitalConfigResponse.setPeriodsRange( capitalConfig.getPeriodsRange() );
        if ( capitalConfig.getEnabled() != null ) {
            capitalConfigResponse.setEnabled( Enum.valueOf( FlowCapitalEnable.class, capitalConfig.getEnabled() ) );
        }
        capitalConfigResponse.setUpdatedBy( capitalConfig.getUpdatedBy() );
        capitalConfigResponse.setUpdatedTime( capitalConfig.getUpdatedTime() );

        return capitalConfigResponse;
    }

    protected FlowRouteConfigDTO.FlowRouteConfigReq flowRouteConfigReqToFlowRouteConfigReq(FlowRouteConfigRequest.FlowRouteConfigReq flowRouteConfigReq) {
        if ( flowRouteConfigReq == null ) {
            return null;
        }

        FlowRouteConfigDTO.FlowRouteConfigReq flowRouteConfigReq1 = new FlowRouteConfigDTO.FlowRouteConfigReq();

        flowRouteConfigReq1.setId( flowRouteConfigReq.getId() );
        flowRouteConfigReq1.setCapitalId( flowRouteConfigReq.getCapitalId() );
        flowRouteConfigReq1.setPriority( flowRouteConfigReq.getPriority() );
        flowRouteConfigReq1.setEnabled( flowRouteConfigReq.getEnabled() );
        if ( flowRouteConfigReq.getValid() != null ) {
            flowRouteConfigReq1.setValid( Enum.valueOf( ValidStatus.class, flowRouteConfigReq.getValid() ) );
        }

        return flowRouteConfigReq1;
    }

    protected List<FlowRouteConfigDTO.FlowRouteConfigReq> flowRouteConfigReqListToFlowRouteConfigReqList(List<FlowRouteConfigRequest.FlowRouteConfigReq> list) {
        if ( list == null ) {
            return null;
        }

        List<FlowRouteConfigDTO.FlowRouteConfigReq> list1 = new ArrayList<FlowRouteConfigDTO.FlowRouteConfigReq>( list.size() );
        for ( FlowRouteConfigRequest.FlowRouteConfigReq flowRouteConfigReq : list ) {
            list1.add( flowRouteConfigReqToFlowRouteConfigReq( flowRouteConfigReq ) );
        }

        return list1;
    }
}
